import { AddressResponseDTO, CompanyDTO } from '../../api-client';

export interface ICompanyFields extends CompanyDTO, AddressResponseDTO {
  id?: number;
  name?: string;
  accountsContactName?: string;
  billingEmail?: string;
  primaryAddress?: AddressResponseDTO;
  primaryAddressFormatted?: string;
  accountHolder?: string | null;
  totalUsers?: number;
  totalDocumentsOrdered?: number;
  totalDocumentPrice?: number;
  isActive?: boolean;
  abn?: string;
  acn?: string;
  accountsContactNumber?: string;
  isBillingPrimary?: boolean;
}

export interface AddressFormValue {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number;
  postalCode: number;
}
