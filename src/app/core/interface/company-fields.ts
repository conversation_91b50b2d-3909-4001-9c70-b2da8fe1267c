import { AddressResponseDTO, CompanyDTO } from '../../api-client';
import { IUserFormData } from './user-fields';

export interface ICompanyFields extends CompanyDTO, AddressResponseDTO {
  id?: number;
  name?: string;
  accountsContactName?: string;
  billingEmail?: string;
  primaryAddress?: AddressResponseDTO;
  primaryAddressFormatted?: string;
  accountHolder?: string | null;
  totalUsers?: number;
  totalDocumentsOrdered?: number;
  totalDocumentPrice?: number;
  isActive?: boolean;
  abn?: string;
  acn?: string;
  accountsContactNumber?: string;
  isBillingPrimary?: boolean;
}

export interface AddressFormValue {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number;
  postalCode: number;
}

// Company form interface that extends CompanyDTO with required fields and flattened address
export interface ICompanyFormData
  extends Required<
    Pick<
      CompanyDTO,
      | 'name'
      | 'abn'
      | 'acn'
      | 'billingEmail'
      | 'accountsContactName'
      | 'accountsContactNumber'
    >
  > {
  // Address fields for form handling (flattened from AddressRequestDTO)
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number | string;
  postalCode: number | string;
}

// Billing form interface that extends company form with billing-specific fields
export interface IBillingFormData extends ICompanyFormData {
  sameAsCompanyDetails: boolean;
}

// Onboarding state interface using existing API types
export interface OnboardingState {
  companyDetails: ICompanyFormData | null;
  billingDetails: IBillingFormData | null;
  userDetails: IUserFormData | null;
  currentStep: number;
}
