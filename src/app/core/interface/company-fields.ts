import { AddressResponseDTO, CompanyDTO, CompanyRequestDTO } from '../../api-client';
import { IUserFormData } from './user-fields';

export interface ICompanyFields extends CompanyDTO, AddressResponseDTO {
  id?: number;
  name?: string;
  accountsContactName?: string;
  billingEmail?: string;
  primaryAddress?: AddressResponseDTO;
  primaryAddressFormatted?: string;
  accountHolder?: string | null;
  totalUsers?: number;
  totalDocumentsOrdered?: number;
  totalDocumentPrice?: number;
  isActive?: boolean;
  abn?: string;
  acn?: string;
  accountsContactNumber?: string;
  isBillingPrimary?: boolean;
}

export interface AddressFormValue {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number;
  postalCode: number;
}

// Company form interfaces for onboarding
export interface CompanyFormData {
  name: string;
  abn: string;
  acn: string;
  billingEmail: string;
  accountsContactName: string;
  accountsContactNumber: string;
  addressLine1: string;
  addressLine2: string;
  suburb: string;
  state: number | string;
  postalCode: number | string;
}

export interface BillingFormData extends CompanyFormData {
  sameAsCompanyDetails: boolean;
}

// Onboarding state interface
export interface OnboardingState {
  companyDetails: CompanyFormData | null;
  billingDetails: BillingFormData | null;
  userDetails: IUserFormData | null;
  currentStep: number;
}

// Interface that extends CompanyRequestDTO for form handling
export interface ICompanyFormRequest extends Omit<CompanyRequestDTO, 'primaryAddress' | 'billingAddress'> {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number | string;
  postalCode: number | string;
  billingAddressLine1: string;
  billingAddressLine2?: string;
  billingSuburb?: string;
  billingState: number | string;
  billingPostalCode: number | string;
}
