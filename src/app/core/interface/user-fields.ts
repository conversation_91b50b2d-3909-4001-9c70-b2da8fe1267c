import { UserResponseDTO } from '../../api-client';

export interface IUserFields extends UserResponseDTO {
  fullName?: string;
  isEditing?: boolean;
  roledisplaytext?: string;
}

// User form interface that extends UserResponseDTO with required fields
export interface IUserFormData extends UserResponseDTO{
  phoneNumber: string; // Alias for contactNumber for form compatibility
  role: string; // Role display text for form handling
}

