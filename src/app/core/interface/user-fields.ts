import { UserResponseDTO, UserRequestDTO } from '../../api-client';

export interface IUserFields extends UserResponseDTO {
  fullName?: string;
  isEditing?: boolean;
  roledisplaytext?: string;
}

// User form interface for onboarding
export interface IUserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  role: string;
}

// Interface that extends UserRequestDTO for form handling
export interface IUserFormRequest extends Omit<UserRequestDTO, 'primaryAddress' | 'billingAddress' | 'contactNumber' | 'roleId'> {
  contactNumber: string;
  roleId: number | string;
}
