import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
} from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';
import { Observable, of, Subscription } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSelectModule } from 'ng-zorro-antd/select';

import { CompanyLookupService } from '../../../core/services/company-lookup.service';
import { NotificationService } from '../../../core/services/notification.service';
import { CompanyOnboardingStateService } from '../services/company-onboarding-state.service';
import { COMMON_STRINGS, PHONE_REGEX } from '../../../core/constants/common';
import {
  StateDTO,
  ZipCodeDTO,
  ApiResponseListStateDTO,
  ApiResponseListZipCodeDTO,
} from '../../../api-client';

@Component({
  selector: 'app-company-onboard-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzSelectModule,
  ],
  templateUrl: './company-onboard-form.component.html',
  styleUrl: './company-onboard-form.component.scss',
})
export class CompanyOnboardFormComponent implements OnInit, OnDestroy {
  @Input() isCompanyStep = false;
  @Input() isBillingStep = false;

  @Output() sameAsCompanyDetailsChange = new EventEmitter<boolean>();
  @Output() formValidityChange = new EventEmitter<boolean>();

  companyForm!: FormGroup;
  billingForm!: FormGroup;
  sameAsCompanyDetails = false;
  isLoading = false;
  states$: Observable<StateDTO[]> = of([]);
  postalCode$: Observable<ZipCodeDTO[]> = of([]);

  private subscriptions = new Subscription();

  // Getter to return the current form based on the step
  get currentForm(): FormGroup {
    return this.isBillingStep ? this.billingForm : this.companyForm;
  }

  constructor(
    private fb: FormBuilder,
    private companyLookupService: CompanyLookupService,
    private notification: NotificationService,
    private onboardingStateService: CompanyOnboardingStateService,
  ) {
    this.createForms();
  }

  private createForms(): void {
    this.companyForm = this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.required, Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
    });

    this.billingForm = this.fb.group({
      name: [''],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', Validators.email],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: [''],
      addressLine2: [''],
      suburb: [''],
      state: [''],
      postalCode: [''],
      sameAsCompanyDetails: [false],
    });
  }

  ngOnInit(): void {
    this.loadStateFromService();
    this.fetchStates();
    this.setupFormSubscriptions();
    this.setupCompanyFormWatcher();
    this.setupFormSaving();

    // Ensure billing form is properly initialized regardless of step
    setTimeout(() => {
      this.initializeBillingDetails();
    }, 100); // Small delay to ensure forms are ready
  }

  private initializeBillingDetails(): void {
    // Always ensure billing details exist in state
    const currentState = this.onboardingStateService.getCurrentState();

    if (!currentState.billingDetails) {
      // Initialize with default billing details - set sameAsCompanyDetails to true by default
      const defaultBillingDetails = {
        name: '',
        abn: '',
        acn: '',
        billingEmail: '',
        accountsContactName: '',
        accountsContactNumber: '',
        addressLine1: '',
        addressLine2: '',
        suburb: '',
        state: '',
        postalCode: '',
        sameAsCompanyDetails: true, // Default to true for easier UX
      };

      this.onboardingStateService.updateBillingDetails(defaultBillingDetails);

      // Update the component property and form control
      this.sameAsCompanyDetails = true;
      if (this.billingForm) {
        this.billingForm
          .get('sameAsCompanyDetails')
          ?.setValue(true, { emitEvent: false });
      }
    }

    // If it's billing step, ensure billing form is properly set up
    if (this.isBillingStep) {
      this.saveBillingFormToState();
      if (this.sameAsCompanyDetails) {
        this.copyCompanyDetailsToBilling();
      }
    }
  }

  private saveBillingFormToState(): void {
    if (this.billingForm) {
      const billingValue = this.billingForm.value;
      this.onboardingStateService.updateBillingDetails({
        ...billingValue,
        sameAsCompanyDetails: this.sameAsCompanyDetails,
      });
    }
  }

  private setupFormSaving(): void {
    // Save company form data to state service on changes
    if (this.companyForm) {
      this.subscriptions.add(
        this.companyForm.valueChanges.subscribe((value) => {
          this.onboardingStateService.updateCompanyDetails(value);

          // If same as company details is checked and we're on billing step, update billing form
          if (this.sameAsCompanyDetails && this.isBillingStep) {
            this.copyCompanyDetailsToBilling();
          }
        }),
      );
    }

    // Save billing form data to state service on changes
    if (this.billingForm) {
      this.subscriptions.add(
        this.billingForm.valueChanges.subscribe((value) => {
          const billingData = {
            ...value,
            sameAsCompanyDetails: this.sameAsCompanyDetails,
          };
          this.onboardingStateService.updateBillingDetails(billingData);
        }),
      );
    }
  }

  private loadStateFromService(): void {
    const state = this.onboardingStateService.getCurrentState();

    // Always load company details to company form
    if (state.companyDetails && this.companyForm) {
      this.companyForm.patchValue(state.companyDetails, { emitEvent: false });
    }

    // Load billing details if available
    if (this.billingForm) {
      if (state.billingDetails) {
        this.billingForm.patchValue(state.billingDetails, { emitEvent: false });
        this.sameAsCompanyDetails =
          state.billingDetails.sameAsCompanyDetails || false;
      } else if (this.isBillingStep) {
        // If no billing details exist but we're on billing step, initialize with default values
        this.billingForm.patchValue(
          {
            sameAsCompanyDetails: false,
          },
          { emitEvent: false },
        );
        this.sameAsCompanyDetails = false;
      }

      // Sync the property with the form control value
      this.sameAsCompanyDetails =
        this.billingForm.get('sameAsCompanyDetails')?.value || false;
    }
  }

  private setupCompanyFormWatcher(): void {
    // Watch for changes in company form to auto-update billing form if "same as company details" is checked
    if (this.companyForm) {
      this.subscriptions.add(
        this.companyForm.valueChanges.subscribe(() => {
          if (this.sameAsCompanyDetails && this.billingForm) {
            this.copyCompanyDetailsToBilling();
          }
        }),
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private setupFormSubscriptions(): void {
    // Setup subscription for company form state changes
    if (this.companyForm) {
      this.subscriptions.add(
        this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
          if (stateId && !this.isBillingStep) {
            this.fetchZipcodes(stateId);
            this.companyForm.get('postalCode')?.enable({ emitEvent: false });
          } else if (!stateId && !this.isBillingStep) {
            this.postalCode$ = of([]);
            this.companyForm.get('postalCode')?.reset();
            this.companyForm.get('postalCode')?.disable({ emitEvent: false });
          }
        }),
      );
    }

    // Setup subscription for billing form state changes
    if (this.billingForm) {
      this.subscriptions.add(
        this.billingForm.get('state')?.valueChanges.subscribe((stateId) => {
          if (stateId && this.isBillingStep && !this.sameAsCompanyDetails) {
            this.fetchZipcodes(stateId);
            this.billingForm.get('postalCode')?.enable({ emitEvent: false });
          } else if (
            (!stateId || this.sameAsCompanyDetails) &&
            this.isBillingStep
          ) {
            this.postalCode$ = of([]);
            this.billingForm.get('postalCode')?.reset();
            this.billingForm.get('postalCode')?.disable({ emitEvent: false });
          }
        }),
      );

      // Setup subscription for checkbox changes
      this.subscriptions.add(
        this.billingForm
          .get('sameAsCompanyDetails')
          ?.valueChanges.subscribe((value) => {
            if (value !== this.sameAsCompanyDetails) {
              this.sameAsCompanyDetails = value;
              this.sameAsCompanyDetailsChange.emit(this.sameAsCompanyDetails);

              // Handle the checkbox change logic
              if (this.sameAsCompanyDetails) {
                this.refreshCompanyDataFromState();
                this.copyCompanyDetailsToBilling();
              } else {
                this.clearBillingForm();
              }

              // Save to state
              setTimeout(() => {
                this.saveBillingFormToState();
              }, 50);
            }
          }),
      );
    }
  }

  onSameAsCompanyDetailsChange(): void {
    // Get the current value from the form control
    const checkboxValue = this.billingForm.get('sameAsCompanyDetails')?.value;
    this.sameAsCompanyDetails = checkboxValue;

    this.sameAsCompanyDetailsChange.emit(this.sameAsCompanyDetails);

    // If checkbox is checked, copy company details to billing form
    if (this.sameAsCompanyDetails) {
      // Refresh company data from state service first
      this.refreshCompanyDataFromState();
      this.copyCompanyDetailsToBilling();
    } else if (!this.sameAsCompanyDetails && this.billingForm) {
      // If unchecked, clear the billing form
      this.clearBillingForm();
    }

    // Always save the updated billing details to state after checkbox change
    setTimeout(() => {
      this.saveBillingFormToState();
    }, 50);
  }

  private refreshCompanyDataFromState(): void {
    const state = this.onboardingStateService.getCurrentState();
    if (state.companyDetails && this.companyForm) {
      this.companyForm.patchValue(state.companyDetails, { emitEvent: false });
    }
  }

  private copyCompanyDetailsToBilling(): void {
    if (!this.billingForm) {
      return;
    }

    const state = this.onboardingStateService.getCurrentState();
    const companyData = state.companyDetails;

    const dataSource = companyData || this.companyForm?.value;

    if (!dataSource) {
      return;
    }

    this.billingForm.patchValue(
      {
        name: dataSource.name || '',
        abn: dataSource.abn || '',
        acn: dataSource.acn || '',
        billingEmail: dataSource.billingEmail || '',
        accountsContactName: dataSource.accountsContactName || '',
        accountsContactNumber: dataSource.accountsContactNumber || '',
        addressLine1: dataSource.addressLine1 || '',
        addressLine2: dataSource.addressLine2 || '',
        suburb: dataSource.suburb || '',
        state: dataSource.state || '',
        postalCode: dataSource.postalCode || '',
      },
      { emitEvent: false },
    );

    if (dataSource.state) {
      this.fetchZipcodes(dataSource.state);
    }

    this.updateBillingFormValidators();

    this.saveBillingFormToState();
  }

  private updateBillingFormValidators(): void {
    if (!this.billingForm) return;

    const fields = [
      'name',
      'abn',
      'acn',
      'billingEmail',
      'accountsContactName',
      'accountsContactNumber',
      'addressLine1',
      'addressLine2',
      'suburb',
      'state',
      'postalCode',
    ];

    if (this.sameAsCompanyDetails) {
      fields.forEach((field) => {
        const control = this.billingForm.get(field);
        if (control) {
          control.clearValidators();
          control.disable({ emitEvent: false });
        }
      });

      this.billingForm.get('billingEmail')?.setValidators([Validators.email]);
    } else {
      fields.forEach((field) => {
        const control = this.billingForm.get(field);
        if (control) {
          control.enable({ emitEvent: false });

          if (field === 'billingEmail') {
            control.setValidators([Validators.required, Validators.email]);
          } else if (
            ['abn', 'acn', 'addressLine1', 'state', 'postalCode'].includes(
              field,
            )
          ) {
            control.setValidators([Validators.required]);
          } else if (field === 'accountsContactNumber') {
            control.setValidators([Validators.pattern(PHONE_REGEX)]);
          } else {
            control.clearValidators();
          }
        }
      });

      if (!this.billingForm.get('state')?.value) {
        this.billingForm.get('postalCode')?.disable({ emitEvent: false });
      }
    }

    fields.forEach((field) => {
      this.billingForm.get(field)?.updateValueAndValidity({ emitEvent: false });
    });
  }

  private clearBillingForm(): void {
    if (!this.billingForm) return;
    this.billingForm.patchValue({
      name: '',
      abn: '',
      acn: '',
      billingEmail: '',
      accountsContactName: '',
      accountsContactNumber: '',
      addressLine1: '',
      addressLine2: '',
      suburb: '',
      state: '',
      postalCode: '',
    });
    this.postalCode$ = of([]);
  }

  fetchStates(): void {
    this.isLoading = true;
    this.states$ = this.companyLookupService.fetchStates().pipe(
      map((response: ApiResponseListStateDTO): StateDTO[] => {
        this.isLoading = false;
        const states = response.data || [];
        if (states.length === 0) {
          this.notification.warning(
            COMMON_STRINGS.errorMessages.failedToFetchStates,
          );
        }
        return states;
      }),
      catchError(() => {
        this.isLoading = false;
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchStates,
        );
        return of([]);
      }),
    );
  }

  fetchZipcodes(stateId: number): void {
    if (!stateId) {
      this.resetAndDisablePostalCodes(
        COMMON_STRINGS.warningMessages.selectValidState,
      );
      return;
    }
    this.isLoading = true;
    this.postalCode$ = this.companyLookupService.fetchZipcodes(stateId).pipe(
      map((response: ApiResponseListZipCodeDTO): ZipCodeDTO[] => {
        this.isLoading = false;
        const zipcodes = response.data || [];

        if (zipcodes.length === 0) {
          this.resetAndDisablePostalCodes(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
        } else {
          this.enablePostalCodes();
        }

        return zipcodes;
      }),
      catchError(() => {
        this.isLoading = false;
        this.resetAndDisablePostalCodes(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
        return of([]);
      }),
    );
  }

  private resetAndDisablePostalCodes(warningMessage: string): void {
    this.postalCode$ = of([]);
    this.notification.warning(warningMessage);

    const currentForm = this.currentForm;
    if (currentForm) {
      currentForm.get('postalCode')?.reset('', { emitEvent: false });
      currentForm.get('postalCode')?.disable({ emitEvent: false });
    }
  }

  private enablePostalCodes(): void {
    const currentForm = this.currentForm;
    if (currentForm) {
      currentForm.get('postalCode')?.enable({ emitEvent: false });
    }
  }
}
