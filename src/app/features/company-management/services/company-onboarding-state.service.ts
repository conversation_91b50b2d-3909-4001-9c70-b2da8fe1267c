import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface CompanyFormData {
  name: string;
  abn: string;
  acn: string;
  billingEmail: string;
  accountsContactName: string;
  accountsContactNumber: string;
  addressLine1: string;
  addressLine2: string;
  suburb: string;
  state: number | string; // Can be number (ID) or string
  postalCode: number | string; // Can be number (ID) or string
}

export interface BillingFormData {
  name: string;
  abn: string;
  acn: string;
  billingEmail: string;
  accountsContactName: string;
  accountsContactNumber: string;
  addressLine1: string;
  addressLine2: string;
  suburb: string;
  state: number | string; // Can be number (ID) or string
  postalCode: number | string; // Can be number (ID) or string
  sameAsCompanyDetails: boolean;
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  role: string;
  // Add other user fields as needed
}

export interface OnboardingState {
  companyDetails: CompanyFormData | null;
  billingDetails: BillingFormData | null;
  userDetails: UserFormData | null;
  currentStep: number;
}

@Injectable({
  providedIn: 'root',
})
export class CompanyOnboardingStateService {
  private initialState: OnboardingState = {
    companyDetails: null,
    billingDetails: null,
    userDetails: null,
    currentStep: 0,
  };

  private stateSubject = new BehaviorSubject<OnboardingState>(
    this.initialState,
  );
  public state$ = this.stateSubject.asObservable();

  constructor() {
    // Constructor intentionally empty
  }

  // Get current state
  getCurrentState(): OnboardingState {
    return this.stateSubject.value;
  }

  // Update company details
  updateCompanyDetails(companyData: CompanyFormData): void {
    const currentState = this.getCurrentState();
    this.stateSubject.next({
      ...currentState,
      companyDetails: companyData,
    });
  }

  // Update billing details
  updateBillingDetails(billingData: BillingFormData): void {
    const currentState = this.getCurrentState();
    this.stateSubject.next({
      ...currentState,
      billingDetails: billingData,
    });
  }

  // Update user details
  updateUserDetails(userData: UserFormData): void {
    const currentState = this.getCurrentState();
    this.stateSubject.next({
      ...currentState,
      userDetails: userData,
    });
  }

  // Update current step
  updateCurrentStep(step: number): void {
    const currentState = this.getCurrentState();
    this.stateSubject.next({
      ...currentState,
      currentStep: step,
    });
  }

  // Get company details
  getCompanyDetails(): CompanyFormData | null {
    return this.getCurrentState().companyDetails;
  }

  // Get billing details
  getBillingDetails(): BillingFormData | null {
    return this.getCurrentState().billingDetails;
  }

  // Get user details
  getUserDetails(): UserFormData | null {
    return this.getCurrentState().userDetails;
  }

  // Reset state
  resetState(): void {
    this.stateSubject.next(this.initialState);
  }

  // Check if all required data is available for final submission
  isReadyForSubmission(): boolean {
    const state = this.getCurrentState();
    return !!(
      state.companyDetails &&
      state.billingDetails &&
      state.userDetails
    );
  }
}
