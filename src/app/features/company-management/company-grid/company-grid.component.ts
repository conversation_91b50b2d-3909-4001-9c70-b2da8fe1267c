import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { CommonModule } from '@angular/common';
import { Observable, of, Subject, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  retry,
  delay,
} from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';

import { Router, ActivatedRoute } from '@angular/router';
import { StateDTO, AddressResponseDTO, CompanyDTO } from '../../../api-client';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { COMPANY_TABLE_COLUMNS } from '../../../core/tableColumns/company.column';
import { DataGridComponent } from '../../data-grid/data-grid.component';
import { CompanyComponent } from '../company/company.component';
import { EditCompanyComponent } from '../edit-company/edit-company.component';

@Component({
  selector: 'app-company-grid',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzCheckboxModule,
    NzButtonModule,
    DataGridComponent,
    CompanyComponent,
    EditCompanyComponent,
  ],
  templateUrl: './company-grid.component.html',
  styleUrls: [
    './company-grid.component.scss',
    '../company/company.component.scss',
  ],
})
export class CompanyGridComponent implements OnInit, OnDestroy {
  isLoading = false;
  totalCompanies = 0;
  newCompanies = 0;
  discontinuedCompanies = 0;
  allCompanies: ICompanyFields[] = [];
  filterActive = false;
  filterInactive = false;
  filterAll = true;
  companyTableColumns = COMPANY_TABLE_COLUMNS;
  companyTableData: ICompanyFields[] = [];
  showOnboarding = false;
  private subscriptions: Subscription = new Subscription();
  openEditForm = false;
  selectedCompany: ICompanyFields | null = null;
  selectedCompanyId?: number | null = null;
  states$: Observable<StateDTO[]> = of([]);

  constructor(
    private registerService: RegisterService,
    private notification: NotificationService,
    private companyStateService: CompanyStateService,
    private sharedLookupService: SharedLookupService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.fetchCompanies();
    this.setupToggleDebounce();
  }

  private setupToggleDebounce(): void {
    // Set up debounced toggle subscription
    this.subscriptions.add(
      this.toggleSubject
        .pipe(
          debounceTime(300), // 300ms debounce
          distinctUntilChanged(
            (prev, curr) =>
              prev.companyId === curr.companyId &&
              prev.currentStatus === curr.currentStatus,
          ),
        )
        .subscribe(({ companyId, currentStatus }) => {
          this.performToggle(companyId, currentStatus);
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.toggleInProgress.clear(); // Clear toggle tracking
    this.toggleSubject.complete(); // Complete the subject
  }

  fetchCompanies(): void {
    this.isLoading = true;
    this.registerService.getAllCompanies().subscribe({
      next: (response) => {
        const companies = Array.isArray(response.data?.content)
          ? response.data.content
          : [];
        this.allCompanies = companies;
        this.totalCompanies =
          response.data?.totalElements || this.allCompanies.length;
        this.newCompanies = this.allCompanies.filter((c) => c.isActive).length;
        this.discontinuedCompanies = this.allCompanies.filter(
          (c) => !c.isActive,
        ).length;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error: HttpErrorResponse) => {
        this.notification.error('Failed to fetch companies');
        console.error('Error fetching companies:', error);
        this.isLoading = false;
      },
    });
  }

  applyFilters() {
    let filteredCompanies = [...this.allCompanies];
    const mappedCompanies: ICompanyFields[] = filteredCompanies.map(
      (company) => ({
        ...company,
        primaryAddressFormatted:
          this.formatAddress(company.primaryAddress) || 'N/A',
        name: company.name || 'N/A',
        accountsContactName: company.accountsContactName || 'N/A',
        billingEmail: company.billingEmail || 'N/A',
        activeUserCount: company.activeUserCount || 0,
        totalUsers: company.totalUsers || company.activeUserCount || 0, // Use totalUsers or fallback to activeUserCount
        totalDocumentsOrdered: company.totalDocumentsOrdered || 0,
        totalDocumentPrice: company.totalDocumentPrice || 0,
        isActive: company.isActive || false,
      }),
    );
    if (this.filterAll || (!this.filterActive && !this.filterInactive)) {
      this.companyTableData = mappedCompanies;
    } else {
      filteredCompanies = filteredCompanies.filter((c) => {
        if (this.filterActive && c.isActive) return true;
        if (this.filterInactive && !c.isActive) return true;
        return false;
      });
      this.companyTableData = filteredCompanies.map((company) => ({
        ...company,
        primaryAddressFormatted:
          this.formatAddress(company.primaryAddress) || 'N/A',
        name: company.name || 'N/A',
        accountsContactName: company.accountsContactName || 'N/A',
        billingEmail: company.billingEmail || 'N/A',
        activeUserCount: company.activeUserCount || 0,
        totalUsers: company.totalUsers || company.activeUserCount || 0, // Use totalUsers or fallback to activeUserCount
        totalDocumentsOrdered: company.totalDocumentsOrdered || 0,
        totalDocumentPrice: company.totalDocumentPrice || 0,
        isActive: company.isActive || false,
      }));
    }
  }

  private formatAddress(address?: AddressResponseDTO): string {
    if (!address) return 'N/A';
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.suburb,
      address.stateName,
      address.zipCode,
    ].filter((part) => part);
    return parts.join(', ') || 'N/A';
  }

  onFilterActiveChange(checked: boolean): void {
    this.filterActive = checked;
    if (checked) {
      this.filterAll = false;
      this.filterInactive = false;
    } else if (!this.filterInactive) {
      this.filterAll = true;
    }
    this.applyFilters();
  }

  onFilterInactiveChange(checked: boolean): void {
    this.filterInactive = checked;
    if (checked) {
      this.filterAll = false;
      this.filterActive = false;
    } else if (!this.filterActive) {
      this.filterAll = true;
    }
    this.applyFilters();
  }

  onFilterAllChange(checked: boolean): void {
    this.filterAll = checked;
    if (checked) {
      this.filterActive = false;
      this.filterInactive = false;
    }
    this.applyFilters();
  }

  onTableDataClick(data: ITableDataClickOutput<ICompanyFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'view':
        this.viewCompany(rowData);
        break;
      case 'Edit':
        this.editDrawerOpen(rowData);
        break;
      case 'toggle':
        this.toggleCompanyStatus(rowData);
        break;
    }
  }

  private toggleInProgress = new Set<number>(); // Track companies being toggled
  private toggleSubject = new Subject<{
    companyId: number;
    currentStatus: boolean;
  }>(); // Debounce subject

  toggleCompanyStatus(rowData: ICompanyFields): void {
    if (!rowData.id) {
      this.notification.error('Company ID not found');
      return;
    }

    // Get the current status from the actual data source
    const currentCompany = this.allCompanies.find((c) => c.id === rowData.id);
    if (!currentCompany) {
      this.notification.error('Company not found in data');
      return;
    }

    const currentStatus = currentCompany.isActive ?? false;
    const willDeactivate = currentStatus === true; // Currently active, will be deactivated

    // Show confirmation for deactivation since it affects users
    if (willDeactivate) {
      const companyName = currentCompany.name || 'this company';
      if (
        confirm(
          `Are you sure you want to deactivate ${companyName}? This will also deactivate all associated users and may take a moment to complete.`,
        )
      ) {
        this.toggleSubject.next({
          companyId: rowData.id,
          currentStatus: currentStatus,
        });
      }
    } else {
      // No confirmation needed for activation
      this.toggleSubject.next({
        companyId: rowData.id,
        currentStatus: currentStatus,
      });
    }
  }

  private performToggle(companyId: number, currentStatus: boolean): void {
    // Prevent multiple simultaneous toggles for the same company
    if (this.toggleInProgress.has(companyId)) {
      return;
    }

    const newStatus = !currentStatus;
    const actionText = newStatus ? 'activating' : 'deactivating';

    // Mark this company as being toggled
    this.toggleInProgress.add(companyId);

    // Show loading message for user feedback
    this.notification.info(
      `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} company...`,
    );

    this.registerService
      .activeInactiveCompany(companyId, newStatus)
      .pipe(
        retry({
          count: 2, // Retry up to 2 times
          delay: (_error, retryCount) => {
            return of(null).pipe(delay(1000 * retryCount)); // Exponential backoff: 1s, 2s
          },
        }),
      )
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Show success message
            const statusText = newStatus ? 'activated' : 'deactivated';
            this.notification.success(`Company ${statusText} successfully`);

            // Refresh data from server to ensure consistency
            this.fetchCompanies();
          } else {
            // Handle API response failure
            const errorMessage = response.message || 'Unknown error occurred';
            console.error('API response indicates failure:', response);

            if (
              errorMessage.includes('Keycloak') ||
              errorMessage.includes('user statuses')
            ) {
              this.notification.error(
                `Failed to ${actionText} company: Some users could not be updated in the authentication system. Please try again or contact support.`,
              );
            } else {
              this.notification.error(
                `Failed to ${actionText} company: ${errorMessage}`,
              );
            }
          }

          // Remove from toggle tracking
          this.toggleInProgress.delete(companyId);
        },
        error: (error) => {
          console.error('Error updating company status:', error);

          // Extract meaningful error message
          let errorMessage = 'Network error occurred';

          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          // Provide specific feedback for Keycloak/user-related errors
          if (
            errorMessage.includes('Keycloak') ||
            errorMessage.includes('user statuses') ||
            errorMessage.includes('Failed to deactivate user')
          ) {
            this.notification.error(
              `Failed to ${actionText} company: Unable to update user access permissions. This may be due to active user sessions or authentication system issues. Please try again in a few moments.`,
            );
          } else if (errorMessage.includes('CompanyException')) {
            this.notification.error(
              `Failed to ${actionText} company: ${errorMessage.split(':').pop()?.trim() || 'Company operation failed'}`,
            );
          } else {
            this.notification.error(
              `Failed to ${actionText} company: ${errorMessage}`,
            );
          }

          // Remove from toggle tracking
          this.toggleInProgress.delete(companyId);

          // Refresh data to ensure UI reflects actual server state
          this.fetchCompanies();
        },
      });
  }

  viewCompany(rowData: ICompanyFields): void {
    if (rowData.id) {
      this.companyStateService.setCompanyData(rowData);
      this.router.navigate([rowData.id], { relativeTo: this.route });
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.failedToOpenCompanyView,
      );
    }
  }

  editDrawerOpen(rowData: ICompanyFields): void {
    this.selectedCompany = rowData;
    this.openEditForm = true;
  }

  showOnboardingForm(): void {
    this.showOnboarding = true;
  }

  closeOnboardingForm(): void {
    this.showOnboarding = false;
  }

  editDrawerClose(): void {
    this.openEditForm = false;
    this.selectedCompany = null;
  }

  onCompanyUpdated(updatedCompany: CompanyDTO): void {
    this.fetchCompanies();
    this.editDrawerClose();
    this.notification.success(
      `Company "${updatedCompany.name}" updated successfully!`,
    );
  }

  onCompanyCreated(newCompany: CompanyDTO): void {
    // Add the new company to the beginning of the array
    this.allCompanies = [newCompany, ...this.allCompanies];

    // Update counters
    this.totalCompanies = this.allCompanies.length;
    this.newCompanies = this.allCompanies.filter((c) => c.isActive).length;
    this.discontinuedCompanies = this.allCompanies.filter(
      (c) => !c.isActive,
    ).length;

    // Refresh the table data to show the new company at the top
    this.applyFilters();

    // Close the onboarding form
    this.closeOnboardingForm();

    // Show success notification
    this.notification.success(
      `Company "${newCompany.name}" created successfully and added to the top of the list!`,
    );
  }
}
