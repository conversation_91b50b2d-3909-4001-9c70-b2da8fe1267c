import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, Subscription } from 'rxjs';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

// Import required services and interfaces

import {
  CompanyDTO,
  RoleDTO,
  ApiResponseObject,
  ApiResponseUserResponseDTO,
  UserRequestDTO,
} from '../../../api-client';
import { NotificationService } from '../../../core/services/notification.service';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { IUserFields } from '../../../core/interface/user-fields';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { RegisterService } from '../../../core/services/register.service';
import { RoleService } from '../../../core/services/role.service';

@Component({
  selector: 'app-add-user',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss',
})
export class AddUserComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() companyId?: number | null = null;
  @Input() company: CompanyDTO | null = null;
  @Input() existingUsers: IUserFields[] = [];
  @Output() userAdded = new EventEmitter<IUserFields>();
  @Output() drawerClosed = new EventEmitter<void>();

  addUserForm: FormGroup;
  isLoading = false;
  roles$: Observable<RoleDTO[]> = of([]);
  roles: RoleDTO[] = [];
  selectedFile: File | null = null;
  private fileSelectedSubscription: Subscription = new Subscription();

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private registerService: RegisterService,
    private roleService: RoleService,
    private companyStateService: CompanyStateService,
    private httpClient: HttpClient,
  ) {
    this.addUserForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: ['', Validators.pattern(PHONE_REGEX)],
      roleId: [null, Validators.required],
      profilePictureUrl: [null],
    });

    this.fileSelectedSubscription =
      this.companyStateService.fileSelected$.subscribe(({ fileData }) => {
        if (fileData) {
          this.addUserForm.get('profilePictureUrl')?.setValue(fileData);
        }
      });
  }

  ngOnInit(): void {
    this.fetchRoles();
  }

  ngOnDestroy(): void {
    if (this.fileSelectedSubscription) {
      this.fileSelectedSubscription.unsubscribe();
    }
  }

  toggleAddUserDrawer(): void {
    if (this.isVisible) {
      this.drawerClosed.emit();
    }
    this.addUserForm.reset();
  }

  fetchRoles(): Observable<RoleDTO[]> {
    this.isLoading = true;
    this.roleService.getAllRoles().subscribe({
      next: (response: ApiResponseObject) => {
        this.isLoading = false;
        const roles = ((response.data as { content?: RoleDTO[] })?.content ||
          []) as RoleDTO[];
        // Filter only Admin and Member roles
        this.roles = roles.filter(
          (role) =>
            role.displayText === 'Admin' || role.displayText === 'Member',
        );
        this.roles$ = of(this.roles);
        if (this.roles.length < 2) {
          this.notification.error(
            COMMON_STRINGS.errorMessages.displayRolesFailed,
          );
        }
      },
      error: () => {
        this.isLoading = false;
        this.notification.error(
          COMMON_STRINGS.errorMessages.displayRolesFailed,
        );
      },
    });
    return this.roles$;
  }

  addUser(): void {
    if (!this.isAddUserFormValid()) {
      this.handleInvalidAddUserForm();
      return;
    }

    const userData = this.buildUserRequest();
    this.isLoading = true;

    // Create user
    this.registerService.createUser(userData).subscribe({
      next: (createUserResponse: ApiResponseUserResponseDTO) => {
        this.handleCreateUserSuccess(createUserResponse);
        this.selectedFile = null;
        this.addUserForm.reset();
      },
      error: (error: HttpErrorResponse) => {
        console.error('Error creating user:', error.status, error.error);
        this.notification.error(error.error?.message || 'Failed to add user');
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  private isAddUserFormValid(): boolean {
    return this.addUserForm.valid && !!this.companyId;
  }

  private handleInvalidAddUserForm(): void {
    this.addUserForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private buildUserRequest(): UserRequestDTO {
    return {
      ...this.addUserForm.value,
      companyId: this.companyId!,
      userType: 'COMPANY',
      password: '',
      primaryAddress: this.company?.primaryAddress || undefined,
      billingAddress: this.company?.billingAddress || undefined,
      isBillingPrimary: false,
      isActive: true,
    };
  }

  private handleCreateUserSuccess(
    createUserResponse: ApiResponseUserResponseDTO,
  ): void {
    const userData = createUserResponse.data;
    if (!userData) {
      this.handleCreateUserError();
      return;
    }

    const roleName = this.getRoleName(userData.roleId || 0);
    const newUser: IUserFields = {
      id: userData.id,
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      contactNumber: userData.contactNumber,
      roleId: userData.roleId,
      profilePictureUrl: userData.profilePictureUrl,
      isActive: userData.isActive,
      fullName: `${userData.firstName} ${userData.lastName}`,
      roledisplaytext: roleName,
    };

    this.notification.success(COMMON_STRINGS.register.userSuccess);
    this.addUserForm.reset();
    this.toggleAddUserDrawer();
    this.isLoading = false;

    // Emit the new user to parent component
    this.userAdded.emit(newUser);
  }

  private handleCreateUserError(): void {
    this.notification.error(COMMON_STRINGS.warningMessages.userCreationFailed);
    this.isLoading = false;
  }

  private getRoleName(roleId: number): string {
    const role = this.roles.find((r) => r.id === roleId);
    return role?.displayText || 'N/A';
  }

  getErrorTip(controlName: string): string | undefined {
    const control = this.addUserForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'email' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'contactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  triggerFileInput(index: number): void {
    this.companyStateService.triggerFileInput(index);
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.selectedFile = file;

      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        this.addUserForm.get('profilePictureUrl')?.setValue(e.target?.result);
      };
      reader.readAsDataURL(file);

      this.companyStateService.fileUploadEvent(event);
    }
  }

  isUserFormValid(): boolean {
    const userform = this.addUserForm;
    const email = this.addUserForm.get('email')?.value;
    return !!(userform.valid && (!email || this.isEmailUnique(email)));
  }

  isEmailUnique(email: string): boolean {
    return !this.existingUsers.some(
      (user) => user.email?.toLowerCase() === email.toLowerCase(),
    );
  }
}
