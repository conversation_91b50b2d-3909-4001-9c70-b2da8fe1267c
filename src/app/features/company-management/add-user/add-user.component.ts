import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { Observable, of, Subscription } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

// Import required services and interfaces

import {
  CompanyDTO,
  RoleDTO,
  UserResponseDTO,
} from '../../../api-client';
import { NotificationService } from '../../../core/services/notification.service';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { IUserFields } from '../../../core/interface/user-fields';
import { CompanyStateService } from '../../../core/services/company-state.service';

import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { UserCreationService, UserCreationRequest } from '../../../core/services/user-creation.service';

@Component({
  selector: 'app-add-user',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss',
})
export class AddUserComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() companyId?: number | null = null;
  @Input() company: CompanyDTO | null = null;
  @Input() existingUsers: IUserFields[] = [];
  @Output() userAdded = new EventEmitter<IUserFields>();
  @Output() drawerClosed = new EventEmitter<void>();

  addUserForm: FormGroup;
  isLoading = false;
  roles$: Observable<RoleDTO[]> = of([]);
  roles: RoleDTO[] = [];
  selectedFile: File | null = null;
  private fileSelectedSubscription: Subscription = new Subscription();

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private sharedLookupService: SharedLookupService,
    private companyStateService: CompanyStateService,
    private userCreationService: UserCreationService,
  ) {
    this.addUserForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: ['', Validators.pattern(PHONE_REGEX)],
      roleId: [null, Validators.required],
      profilePictureUrl: [null],
    });

    this.fileSelectedSubscription =
      this.companyStateService.fileSelected$.subscribe(({ fileData }) => {
        if (fileData) {
          this.addUserForm.get('profilePictureUrl')?.setValue(fileData);
        }
      });
  }

  ngOnInit(): void {
    this.fetchRoles();
  }

  ngOnDestroy(): void {
    if (this.fileSelectedSubscription) {
      this.fileSelectedSubscription.unsubscribe();
    }
  }

  toggleAddUserDrawer(): void {
    if (this.isVisible) {
      this.drawerClosed.emit();
    }
    this.addUserForm.reset();
  }

  fetchRoles(): Observable<RoleDTO[]> {
    this.isLoading = true;
    this.roles$ = this.sharedLookupService.fetchRoles().pipe(
      map((roles: RoleDTO[]) => {
        this.isLoading = false;
        this.roles = roles;
        return this.roles;
      }),
      catchError(() => {
        this.isLoading = false;
        return of([]);
      }),
    );
    return this.roles$;
  }

  addUser(): void {
    if (!this.isAddUserFormValid()) {
      this.handleInvalidAddUserForm();
      return;
    }

    this.isLoading = true;

    const request: UserCreationRequest = {
      formData: this.addUserForm.value,
      companyId: this.companyId!,
      company: this.company || undefined,
      selectedFile: this.selectedFile,
      userType: 'COMPANY',
    };

    this.userCreationService.createUserWithProfilePicture(request).subscribe({
      next: (result) => {
        if (result.success && result.userData) {
          this.handleCreateUserSuccess(result.userData);
          this.selectedFile = null;
          this.addUserForm.reset();
        }
      },
      error: (error) => {
        console.error('Error creating user:', error);
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  private isAddUserFormValid(): boolean {
    return this.addUserForm.valid && !!this.companyId;
  }

  private handleInvalidAddUserForm(): void {
    this.addUserForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private handleCreateUserSuccess(userData: UserResponseDTO): void {
    if (!userData) {
      this.handleCreateUserError();
      return;
    }

    const roleName = this.getRoleName(userData.roleId || 0);
    const newUser: IUserFields = {
      id: userData.id,
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      contactNumber: userData.contactNumber,
      roleId: userData.roleId,
      profilePictureUrl: userData.profilePictureUrl,
      isActive: userData.isActive,
      fullName: `${userData.firstName} ${userData.lastName}`,
      roledisplaytext: roleName,
    };

    this.notification.success(COMMON_STRINGS.register.userSuccess);
    this.addUserForm.reset();
    this.toggleAddUserDrawer();
    this.isLoading = false;

    // Emit the new user to parent component
    this.userAdded.emit(newUser);
  }

  private handleCreateUserError(): void {
    this.notification.error(COMMON_STRINGS.warningMessages.userCreationFailed);
    this.isLoading = false;
  }

  private getRoleName(roleId: number): string {
    const role = this.roles.find((r) => r.id === roleId);
    return role?.displayText || 'N/A';
  }

  getErrorTip(controlName: string): string | undefined {
    const control = this.addUserForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'email' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'contactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  triggerFileInput(index: number): void {
    this.companyStateService.triggerFileInput(index);
  }

  onFileSelected(event: Event): void {
    this.userCreationService.handleFileSelection(
      event,
      (file: File, dataUrl: string) => {
        this.selectedFile = file;
        this.addUserForm.get('profilePictureUrl')?.setValue(dataUrl);
        this.companyStateService.fileUploadEvent(event);
      },
    );
  }

  isUserFormValid(): boolean {
    const userform = this.addUserForm;
    const email = this.addUserForm.get('email')?.value;
    return !!(userform.valid && (!email || this.isEmailUnique(email)));
  }

  isEmailUnique(email: string): boolean {
    return !this.existingUsers.some(
      (user) => user.email?.toLowerCase() === email.toLowerCase(),
    );
  }
}
