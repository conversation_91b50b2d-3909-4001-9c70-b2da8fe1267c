import { CommonModule } from '@angular/common';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { forkJoin } from 'rxjs';

import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';

import { CompanyOnboardFormComponent } from '../company-onboard-form/company-onboard-form.component';
import { CompanyUserOnboardFormComponent } from '../company-user-onboard-form/company-user-onboard-form.component';
import { PreviewAndSubmitFormComponent } from '../preview-and-submit-form/preview-and-submit-form.component';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { CompanyFormData, BillingFormData } from '../../../core/interface/company-fields';

import { COMMON_STRINGS } from '../../../core/constants/common';
import { OnboardingStep } from '../../../core/constants/company';
import { IUserFields } from '../../../core/interface/user-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';

import {
  CompanyDTO,
  CompanyRequestDTO,
  ApiResponseCompanyDTO,
  ApiResponseObject,
  UserRequestDTO,
  AddressRequestDTO,
} from '../../../api-client';

@Component({
  selector: 'app-company',
  standalone: true,
  imports: [
    CommonModule,
    NzTabsModule,
    NzBreadCrumbModule,
    CompanyOnboardFormComponent,
    CompanyUserOnboardFormComponent,
    PreviewAndSubmitFormComponent,
  ],
  templateUrl: './company.component.html',
  styleUrls: ['./company.component.scss', '../../../../styles.scss'],
})
export class CompanyComponent implements OnInit {
  OnboardingStep = OnboardingStep;
  onboardingSteps = Object.values(OnboardingStep);
  activeStep: OnboardingStep = OnboardingStep.CompanyDetails;
  activeTabIndex = 0;
  tableData: IUserFields[] = [];
  showOnboarding = false;
  isLoading = false;
  errorMessage = '';

  @Output() companyClose = new EventEmitter<CompanyDTO | null>();
  @Output() companyCreated = new EventEmitter<CompanyDTO>();

  constructor(
    private notification: NotificationService,
    private onboardingStateService: CompanyStateService,
    private registerService: RegisterService,
  ) {}

  ngOnInit(): void {
    this.onboardingStateService.resetOnboardingState();
  }

  goToNextStep(): void {
    const currentIndex = this.onboardingSteps.indexOf(this.activeStep);
    if (currentIndex < this.onboardingSteps.length - 1) {
      this.activeStep = this.onboardingSteps[currentIndex + 1];
      this.activeTabIndex = currentIndex + 1;
    }
  }

  goToPreviousStep(): void {
    const currentIndex = this.onboardingSteps.indexOf(this.activeStep);
    if (currentIndex > 0) {
      this.activeStep = this.onboardingSteps[currentIndex - 1];
      this.activeTabIndex = currentIndex - 1;
    }
  }

  isFirstStep(): boolean {
    return this.activeStep === this.onboardingSteps[0];
  }

  isLastStep(): boolean {
    return (
      this.activeStep === this.onboardingSteps[this.onboardingSteps.length - 1]
    );
  }

  onTabIndexChange(index: number): void {
    this.activeTabIndex = index;
    this.activeStep = this.onboardingSteps[index];
  }

  showOnboardingForm(): void {
    this.showOnboarding = true;
    this.activeStep = OnboardingStep.CompanyDetails;
    this.activeTabIndex = 0;
    this.tableData = [];
  }

  cancelOnboarding(): void {
    this.companyClose.emit(null);
    this.showOnboarding = false;
    this.activeStep = OnboardingStep.CompanyDetails;
    this.activeTabIndex = 0;
    this.tableData = [];
  }

  isCurrentStepValid(): boolean {
    switch (this.activeStep) {
      case OnboardingStep.CompanyDetails:
        return this.isCompanyDetailsValid();
      case OnboardingStep.AddPayment:
        return this.isBillingDetailsValid();
      case OnboardingStep.AddUser:
        return this.isUserDetailsValid();
      case OnboardingStep.Preview:
        return true;
      default:
        return false;
    }
  }

  private isCompanyDetailsValid(): boolean {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    const companyDetails = state.companyDetails;

    if (!companyDetails) return false;

    return !!(
      companyDetails.name?.trim() &&
      companyDetails.abn?.trim() &&
      companyDetails.acn?.trim() &&
      companyDetails.billingEmail?.trim() &&
      companyDetails.addressLine1?.trim() &&
      companyDetails.state &&
      companyDetails.postalCode
    );
  }

  private isBillingDetailsValid(): boolean {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    const billingDetails = state.billingDetails;

    if (!billingDetails) return false;
    if (billingDetails.sameAsCompanyDetails) return true;
    return !!(
      billingDetails.abn?.trim() &&
      billingDetails.acn?.trim() &&
      billingDetails.addressLine1?.trim() &&
      billingDetails.state &&
      billingDetails.postalCode
    );
  }

  private isUserDetailsValid(): boolean {
    return this.tableData.length > 0;
  }

  onClickCreate(): void {
    if (!this.isCurrentStepValid()) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.createCompanyAndUsers();
  }

  private createCompanyAndUsers(): void {
    const state = this.onboardingStateService.getCurrentOnboardingState();

    if (
      !state.companyDetails ||
      !state.billingDetails ||
      !this.tableData.length
    ) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      this.isLoading = false;
      return;
    }

    const companyFormValue = state.companyDetails;
    const billingFormValue = state.billingDetails;

    if (!companyFormValue.state || !companyFormValue.postalCode) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      this.isLoading = false;
      return;
    }

    if (
      !billingFormValue.sameAsCompanyDetails &&
      (!billingFormValue.state || !billingFormValue.postalCode)
    ) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      this.isLoading = false;
      return;
    }

    const companyRequest = this.createCompanyRequest();
    this.registerService.createCompany(companyRequest).subscribe({
      next: (companyResponse: ApiResponseCompanyDTO) => {
        this.handleCompanyCreationSuccess(companyResponse);
      },
      error: (companyError: HttpErrorResponse) => {
        this.handleCompanyCreationError(companyError);
      },
    });
  }

  editSection(step: OnboardingStep): void {
    const index = this.onboardingSteps.indexOf(step);
    if (index !== -1) {
      this.activeTabIndex = index;
      this.activeStep = step;
    }
  }

  private handleCompanyCreationSuccess(
    companyResponse: ApiResponseCompanyDTO,
  ): void {
    if (companyResponse.data?.id) {
      const companyId = companyResponse.data.id;
      this.createUsersForCompany(companyId, companyResponse.data);
    } else {
      this.isLoading = false;
      this.notification.error(COMMON_STRINGS.warningMessages.companyIdNotFound);
    }
  }

  private createUsersForCompany(
    companyId: number,
    companyData: CompanyDTO,
  ): void {
    const userRequests = this.createUserRequests(companyId);
    const userCreationObservables = userRequests.map(
      (userRequest: UserRequestDTO) =>
        this.registerService.createUser(userRequest),
    );

    forkJoin(userCreationObservables).subscribe({
      next: (userResponses: ApiResponseObject[]) => {
        this.handleUserCreationSuccess(userResponses, companyData);
      },
      error: (userError: HttpErrorResponse) => {
        this.handleUserCreationError(userError);
      },
    });
  }

  private handleUserCreationSuccess(
    userResponses: ApiResponseObject[],
    companyData: CompanyDTO,
  ): void {
    this.isLoading = false;
    this.notification.success(
      COMMON_STRINGS.successMessages.companyCreated.replace(
        '${userResponses.length}',
        userResponses.length.toString(),
      ),
    );

    const companyWithUserCount = {
      ...companyData,
      activeUserCount: userResponses.length,
      totalUsers: userResponses.length,
    };

    this.companyCreated.emit(companyWithUserCount);

    this.resetOnboardingForms();
    this.showOnboarding = false;
  }

  private resetOnboardingForms(): void {
    this.onboardingStateService.resetOnboardingState();
    this.tableData = [];
    this.activeStep = OnboardingStep.CompanyDetails;
    this.activeTabIndex = 0;
  }

  private createCompanyRequest(): CompanyRequestDTO {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    const companyFormValue = state.companyDetails!;
    const billingFormValue = state.billingDetails!;

    const primaryAddress = this.createPrimaryAddress(companyFormValue);
    const billingAddress = this.createBillingAddress(
      billingFormValue,
      primaryAddress,
    );

    return {
      name: companyFormValue.name,
      primaryAddress,
      billingAddress,
      billingEmail: billingFormValue.sameAsCompanyDetails
        ? companyFormValue.billingEmail
        : billingFormValue.billingEmail,
      accountsContactNumber:
        companyFormValue.accountsContactNumber || undefined,
      accountsContactName: companyFormValue.accountsContactName || undefined,
      abn: companyFormValue.abn || undefined,
      acn: companyFormValue.acn || undefined,
      isBillingPrimary: billingFormValue.sameAsCompanyDetails,
    };
  }

  private createPrimaryAddress(formValue: CompanyFormData): AddressRequestDTO {
    return {
      addressLine1: formValue.addressLine1,
      addressLine2: formValue.addressLine2 || undefined,
      suburb: formValue.suburb || undefined,
      stateId: Number(formValue.state),
      zipCodeId: Number(formValue.postalCode),
    };
  }

  private createBillingAddress(
    billingFormValue: BillingFormData,
    primaryAddress: AddressRequestDTO,
  ): AddressRequestDTO {
    return billingFormValue.sameAsCompanyDetails
      ? { ...primaryAddress }
      : {
          addressLine1: billingFormValue.addressLine1,
          addressLine2: billingFormValue.addressLine2 || undefined,
          suburb: billingFormValue.suburb || undefined,
          stateId: Number(billingFormValue.state),
          zipCodeId: Number(billingFormValue.postalCode),
        };
  }

  private createUserRequests(companyId: number): UserRequestDTO[] {
    const addresses = this.createUserAddresses();
    return this.mapUsersToRequests(
      companyId,
      addresses.primaryAddress,
      addresses.billingAddress,
    );
  }

  private createUserAddresses(): {
    primaryAddress: AddressRequestDTO;
    billingAddress: AddressRequestDTO;
  } {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    const companyFormValue = state.companyDetails!;
    const billingFormValue = state.billingDetails!;

    const primaryAddress: AddressRequestDTO = {
      addressLine1: companyFormValue.addressLine1,
      addressLine2: companyFormValue.addressLine2 || undefined,
      suburb: companyFormValue.suburb || undefined,
      stateId: Number(companyFormValue.state),
      zipCodeId: Number(companyFormValue.postalCode),
    };

    const billingAddress: AddressRequestDTO =
      billingFormValue.sameAsCompanyDetails
        ? { ...primaryAddress }
        : {
            addressLine1: billingFormValue.addressLine1,
            addressLine2: billingFormValue.addressLine2 || undefined,
            suburb: billingFormValue.suburb || undefined,
            stateId: Number(billingFormValue.state),
            zipCodeId: Number(billingFormValue.postalCode),
          };

    return { primaryAddress, billingAddress };
  }

  private mapUsersToRequests(
    companyId: number,
    primaryAddress: AddressRequestDTO,
    billingAddress: AddressRequestDTO,
  ): UserRequestDTO[] {
    return this.tableData.map((user, index) => ({
      email: user.email!,
      password: '',
      firstName: user.firstName!,
      lastName: user.lastName!,
      userType: UserRequestDTO.UserTypeEnum.Company,
      primaryAddress: { ...primaryAddress },
      billingAddress: { ...billingAddress },
      contactNumber: user.contactNumber || undefined,
      companyId,
      roleId: user.roleId!,
      isBillingPrimary: index === 0,
      profilePictureUrl: user.profilePictureUrl || undefined,
      isActive: true,
    }));
  }

  private handleUserCreationError(userError: HttpErrorResponse): void {
    this.isLoading = false;
    this.errorMessage = userError?.error?.message || 'Failed to create users';
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyCreationFailure.replace(
        '${this.errorMessage}',
        this.errorMessage,
      ),
    );
  }

  private handleCompanyCreationError(companyError: HttpErrorResponse): void {
    this.isLoading = false;
    this.errorMessage =
      companyError?.error?.message || 'Failed to create company';
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyCreationFailure.replace(
        '${this.errorMessage}',
        this.errorMessage,
      ),
    );
  }
}
