<div class="onboarding-wrapper">

    <nz-breadcrumb nzSeparator="/" class="breadcrumb">
        <nz-breadcrumb-item>
            <span (click)="cancelOnboarding()" class="non-active-breadcrumb">
                Company Management
            </span>
        </nz-breadcrumb-item>
        <nz-breadcrumb-item>
            <span class="active-breadcrumb">
                Add Company
            </span>
        </nz-breadcrumb-item>
    </nz-breadcrumb>
    <nz-tabset class="company-creation-tabs" [(nzSelectedIndex)]="activeTabIndex"
        (nzSelectedIndexChange)="onTabIndexChange($event)" data-testid="company-creation-tabs">
        <nz-tab class="search-type-tabs" *ngFor="let step of onboardingSteps; let i = index" [nzTitle]="step"
            [attr.data-test-id]="'onboarding-tab-' + i">
            <ng-container [ngSwitch]="step">
                <!-------------------------------------- Company Details ------------------------------------------>
                <ng-container *ngSwitchCase="OnboardingStep.CompanyDetails">
                    <app-company-onboard-form [isCompanyStep]="true" [isBillingStep]="false">
                    </app-company-onboard-form>
                </ng-container>

                <!------------------------------------------------------ add user--------------------------------------------- -->
                <ng-container *ngSwitchCase="OnboardingStep.AddUser" class="add-user-form">
                    <app-company-user-onboard-form
                        [tableData]="tableData"
                        (tableDataChange)="tableData = $event"
                        (userAdded)="onUserAdded()"
                        (userEdited)="onUserEdited()"
                        (userDeleted)="onUserDeleted($event)">
                    </app-company-user-onboard-form>
                </ng-container>

                <!-------------------------------------- Payment Details ------------------------------------------->
                <ng-container *ngSwitchCase="OnboardingStep.AddPayment">
                    <app-company-onboard-form [isCompanyStep]="false" [isBillingStep]="true">
                    </app-company-onboard-form>
                </ng-container>

                <!--------------------------------------- Preview & Submit ------------------------------------------>
                <ng-container *ngSwitchCase="OnboardingStep.Preview">
                    <app-preview-and-submit-form
                        [tableData]="tableData"
                        [isLoading]="isLoading"
                        (editSection)="editSection($event)"
                        (createCompany)="onClickCreate()">
                    </app-preview-and-submit-form>
                </ng-container>
            </ng-container>
        </nz-tab>
    </nz-tabset>
    <div class="navigation-buttons mt-6 d-flex" *ngIf="!isLastStep()"
        [ngClass]="{'justify-content-end': isFirstStep(), 'justify-content-between': !isFirstStep()}">

        <button type="button" class="cancel-button" (click)="cancelOnboarding()" *ngIf="isFirstStep()"
            data-testid="cancel-onboarding-button">
            Cancel
        </button>

        <button type="button" class="previous-button" (click)="goToPreviousStep()" *ngIf="!isFirstStep()"
            data-testid="previous-step-button">
            Previous
        </button>

        <button type="button" class="next-button" [disabled]="!isCurrentStepValid()" (click)="goToNextStep()"
            data-testid="next-step-button">
            Next
        </button>
    </div>
</div>
