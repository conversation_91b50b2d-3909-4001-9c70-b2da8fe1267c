<!-- Drawer for Adding User -->
<nz-drawer [nzClosable]="false" class="drawer-header" [nzVisible]="isVisible" [nzTitle]="drawerTitle" [nzExtra]="extra"
    nzPlacement="right" (nzOnClose)="toggleAddUserDrawer()">
    <ng-container *nzDrawerContent>
        <form nz-form nzLayout="vertical" [formGroup]="addUserForm">
            <div nz-row nzGutter="16">
                <div nz-col [nzMd]="24">
                    <div class="image-wrapper position-relative">
                        <img [src]="addUserForm.get('profilePictureUrl')?.value || 'assets/profileImage.png'"
                            alt="User Image" class="rounded-circle mb-2" data-test-id="user-profile-image" />
                        <div class="camera-icon" (click)="triggerFileInput(0)" data-test-id="camera-icon"
                            aria-label="Upload profile image">
                            <i nz-icon nzType="camera" nzTheme="outline"></i>
                        </div>
                        <input type="file" id="fileInput-0" accept="image/*" style="display: none;"
                            (change)="onFileSelected($event)" data-test-id="user-profile-upload" />
                    </div>
                    <nz-form-item>
                        <nz-form-label class="form-label" [nzRequired]="true">First Name</nz-form-label>
                        <nz-form-control [nzErrorTip]="getErrorTip('firstName')">
                            <input class="form-control edit-user-form-field" nz-input placeholder="Enter first name"
                                formControlName="firstName" />
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-form-label class="form-label" [nzRequired]="true">Last Name</nz-form-label>
                        <nz-form-control [nzErrorTip]="getErrorTip('lastName')">
                            <input class="form-control edit-user-form-field" nz-input placeholder="Enter last name"
                                formControlName="lastName" />
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-form-label class="form-label" [nzRequired]="true">Email</nz-form-label>
                        <nz-form-control [nzErrorTip]="getErrorTip('email')">
                            <input class="form-control edit-user-form-field" nz-input placeholder="Enter email"
                                type="email" formControlName="email" />
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-form-label class="form-label" [nzRequired]="true">Telephone</nz-form-label>
                        <nz-form-control [nzErrorTip]="getErrorTip('contactNumber')">
                            <input class="form-control edit-user-form-field" nz-input placeholder="Enter contact number"
                                formControlName="contactNumber" />
                        </nz-form-control>
                    </nz-form-item>
                    <nz-form-item>
                        <nz-form-label class="form-label" [nzRequired]="true">Role</nz-form-label>
                        <nz-form-control [nzErrorTip]="getErrorTip('roleId')">
                            <nz-select class="form-control edit-user-form-field" formControlName="roleId"
                                nzPlaceHolder="Select a role" [nzLoading]="isLoading">
                                <nz-option *ngFor="let role of (roles$ | async)" [nzValue]="role.id"
                                    [nzLabel]="role.displayText || ''"></nz-option>
                            </nz-select>
                        </nz-form-control>
                    </nz-form-item>
                </div>
            </div>
            <div class="user-edit-form-buttons mt-6 d-flex">
                <button nz-button nzType="primary" class="save-edit-button" type="button" (click)="addUser()"
                    [disabled]="!isUserFormValid()">Add User</button>
                <button nz-button nzType="default" class="cancel-edit-button" type="button"
                    (click)="toggleAddUserDrawer()">Cancel</button>
            </div>
        </form>
    </ng-container>
</nz-drawer>
<ng-template #drawerTitle>
    <h2>Add User</h2>
</ng-template>

<!-- Extra close button inside drawer  -->
<ng-template #extra>
    <button nz-button nzType="primary" nzGhost (click)="toggleAddUserDrawer()">
        <nz-icon nzType="close-circle" nzTheme="fill" />
        Close
    </button>
</ng-template>