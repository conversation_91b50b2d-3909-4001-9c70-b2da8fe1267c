import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ChangeDetectorRef,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
  ApiResponseListStateDTO,
  ApiResponseListZipCodeDTO,
  ApiResponseCompanyDTO,
  CompanyRequestDTO,
  AddressResponseDTO,
} from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { CompanyLookupService } from '../../../core/services/company-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';

@Component({
  selector: 'app-edit-company-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company-form.component.html',
  styleUrls: [
    './edit-company-form.component.scss',
    '../view-company/view-company.component.scss',
  ],
})
export class EditCompanyFormComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() company: ICompanyFields | null = null;
  @Output() companySaved = new EventEmitter<CompanyDTO>();
  @Output() formCancelled = new EventEmitter<void>();

  companyForm: FormGroup;
  isLoading = false;
  isBillingDetailsAccordionOpen = false;
  isSameAsCompanyDetails = false;

  states: StateDTO[] = [];
  postalCode: ZipCodeDTO[] = [];
  billingZipcode: ZipCodeDTO[] = [];

  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyStateService: CompanyStateService,
    private companyLookupService: CompanyLookupService,
    private cdr: ChangeDetectorRef,
  ) {
    this.companyForm = this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      billingAddressLine1: ['', Validators.required],
      billingAddressLine2: [''],
      billingSuburb: [''],
      billingState: ['', Validators.required],
      billingPostalCode: ['', Validators.required],
    });
  }

  async ngOnInit(): Promise<void> {
    if (this.company) {
      await this.initializeForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isSaveButtonEnabled(): boolean {
    return this.companyForm.valid && this.companyForm.dirty && !this.isLoading;
  }

  async initializeForm(): Promise<void> {
    if (!this.company) return;

    console.log('🔍 Initializing form with company:', this.company);

    // Fetch states
    await this.fetchStates();

    // Populate form with company data
    this.populateFormWithCompanyData();

    // Fetch zipcodes for primary and billing addresses
    const primaryStateId = this.companyForm.get('state')?.value;
    const billingStateId = this.companyForm.get('billingState')?.value;

    if (primaryStateId) {
      await this.fetchZipcodes(primaryStateId, 'primary');
    }
    if (billingStateId && !this.isSameAsCompanyDetails) {
      await this.fetchZipcodes(billingStateId, 'billing');
    }

    // Setup form subscriptions for state changes
    this.setupFormSubscriptions();

    // Mark form as pristine
    this.companyForm.markAsPristine();
    this.cdr.detectChanges();
  }

  private populateFormWithCompanyData(): void {
    if (!this.company) return;

    console.log('🔍 Populating form with company data:', this.company);
    console.log('🔍 Available states:', this.states);

    // Find state IDs by matching state names
    const primaryState = this.states.find(
      (s) => s.stateName === this.company?.primaryAddress?.stateName,
    );
    const billingState = this.states.find(
      (s) => s.stateName === this.company?.billingAddress?.stateName,
    );

    const primaryStateId = primaryState?.id || null;
    const billingStateId = billingState?.id || null;

    console.log('🔍 Primary state found:', primaryState, 'ID:', primaryStateId);
    console.log('🔍 Billing state found:', billingState, 'ID:', billingStateId);

    // Check if billing address is same as primary address
    this.isSameAsCompanyDetails =
      this.company.primaryAddress?.addressLine1 === this.company.billingAddress?.addressLine1 &&
      this.company.primaryAddress?.addressLine2 === this.company.billingAddress?.addressLine2 &&
      this.company.primaryAddress?.suburb === this.company.billingAddress?.suburb &&
      this.company.primaryAddress?.stateName === this.company.billingAddress?.stateName &&
      this.company.primaryAddress?.zipCodeId === this.company.billingAddress?.zipCodeId;

    // Populate form with company data
    this.companyForm.patchValue({
      name: this.company.name || '',
      abn: this.company.abn || '',
      acn: this.company.acn || '',
      billingEmail: this.company.billingEmail || '',
      accountsContactName: this.company.accountsContactName || '',
      accountsContactNumber: this.company.accountsContactNumber || '',
      addressLine1: this.company.primaryAddress?.addressLine1 || '',
      addressLine2: this.company.primaryAddress?.addressLine2 || '',
      suburb: this.company.primaryAddress?.suburb || '',
      state: primaryStateId,
      postalCode: this.company.primaryAddress?.zipCodeId || '',
      billingAddressLine1: this.company.billingAddress?.addressLine1 || '',
      billingAddressLine2: this.company.billingAddress?.addressLine2 || '',
      billingSuburb: this.company.billingAddress?.suburb || '',
      billingState: billingStateId,
      billingPostalCode: this.company.billingAddress?.zipCodeId || '',
    });

    if (this.isSameAsCompanyDetails) {
      this.disableBillingFields();
    }

    console.log('✅ Form populated with values:', this.companyForm.value);
    this.cdr.detectChanges();
  }

  private setupFormSubscriptions(): void {
    this.subscriptions.add(
      this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        if (stateId) {
          this.fetchZipcodes(stateId, 'primary').then(() => {
            this.companyForm.get('postalCode')?.setValue('');
            if (this.isSameAsCompanyDetails) {
              this.companyForm.get('billingPostalCode')?.setValue('');
              this.billingZipcode = this.postalCode;
            }
            this.cdr.detectChanges();
          });
        }
      }),
    );

    this.subscriptions.add(
      this.companyForm.get('billingState')?.valueChanges.subscribe((stateId) => {
        if (stateId && !this.isSameAsCompanyDetails) {
          this.fetchZipcodes(stateId, 'billing').then(() => {
            this.companyForm.get('billingPostalCode')?.setValue('');
            this.cdr.detectChanges();
          });
        }
      }),
    );

    this.subscriptions.add(
      this.companyForm.valueChanges.subscribe(() => {
        this.cdr.detectChanges();
      }),
    );
  }

  async fetchStates(): Promise<void> {
    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.companyLookupService.fetchStates().subscribe({
          next: (response: ApiResponseListStateDTO) => {
            this.isLoading = false;
            this.states = response.data || [];
            console.log('✅ Fetched states:', this.states);
            if (this.states.length === 0) {
              this.notification.warning(
                COMMON_STRINGS.errorMessages.failedToFetchStates,
              );
            }
            resolve();
          },
          error: (error) => {
            this.isLoading = false;
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToFetchStates,
            );
            console.error('❌ Failed to fetch states:', error);
            reject();
          },
        }),
      );
    });
  }

  async fetchZipcodes(stateId: number, addressType: 'primary' | 'billing'): Promise<void> {
    if (!stateId) {
      console.log(`⚠️ No stateId provided for ${addressType} address`);
      return;
    }

    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.companyLookupService.fetchZipcodes(stateId).subscribe({
          next: (response: ApiResponseListZipCodeDTO) => {
            this.isLoading = false;
            const zipcodes = response.data || [];
            console.log(`✅ Fetched ${addressType} zipcodes for state ${stateId}:`, zipcodes);

            if (addressType === 'primary') {
              this.postalCode = zipcodes;
              if (this.company?.primaryAddress?.zipCodeId) {
                const matchingZipcode = zipcodes.find(
                  (z) => z.id === this.company?.primaryAddress?.zipCodeId,
                );
                if (matchingZipcode) {
                  this.companyForm.get('postalCode')?.setValue(matchingZipcode.id);
                  console.log('✅ Set primary postal code:', matchingZipcode.id);
                } else {
                  console.log('❌ No matching primary zipcode found for ID:', this.company?.primaryAddress?.zipCodeId);
                }
              }
            } else {
              this.billingZipcode = zipcodes;
              if (this.company?.billingAddress?.zipCodeId) {
                const matchingZipcode = zipcodes.find(
                  (z) => z.id === this.company?.billingAddress?.zipCodeId,
                );
                if (matchingZipcode) {
                  this.companyForm.get('billingPostalCode')?.setValue(matchingZipcode.id);
                  console.log('✅ Set billing postal code:', matchingZipcode.id);
                } else {
                  console.log('❌ No matching billing zipcode found for ID:', this.company?.billingAddress?.zipCodeId);
                }
              }
            }

            if (zipcodes.length === 0) {
              this.notification.warning(
                COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
              );
            }
            this.cdr.detectChanges();
            resolve();
          },
          error: (error) => {
            this.isLoading = false;
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
            );
            console.error(`❌ Failed to fetch ${addressType} zipcodes:`, error);
            reject();
          },
        }),
      );
    });
  }

  saveCompanyDetails(): void {
    console.log('🔍 Form valid:', this.companyForm.valid);
    console.log('🔍 Form errors:', this.getFormErrors());
    console.log('🔍 Form values:', this.companyForm.getRawValue());

    if (!this.companyForm.valid) {
      this.handleInvalidForm();
      return;
    }

    const tempCompanyRequest = this.buildCompanyRequest();
    const tempCompany = this.buildTempCompany(tempCompanyRequest);
    if (this.company?.id) {
      this.updateCompanyDetails(tempCompanyRequest, tempCompany);
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
    }
  }

  private getFormErrors(): any {
    const formErrors: any = {};
    Object.keys(this.companyForm.controls).forEach((key) => {
      const controlErrors = this.companyForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });
    return formErrors;
  }

  cancelEdit(): void {
    this.companyStateService.clearTempCompanyData();
    this.companyForm.reset();
    this.isSameAsCompanyDetails = false;
    this.isBillingDetailsAccordionOpen = false;
    this.formCancelled.emit();
  }

  private handleInvalidForm(): void {
    this.companyForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private buildCompanyRequest(): CompanyRequestDTO {
    const formValue = this.companyForm.getRawValue();

    console.log('🔍 Building company request with form values:', formValue);

    const primaryStateId = Number(formValue.state);
    const primaryZipCodeId = Number(formValue.postalCode);
    const billingStateId = Number(formValue.billingState);
    const billingZipCodeId = Number(formValue.billingPostalCode);

    console.log('🔍 Parsed IDs:', {
      primaryStateId,
      primaryZipCodeId,
      billingStateId,
      billingZipCodeId,
      isSameAsCompanyDetails: this.isSameAsCompanyDetails,
    });

    const request: CompanyRequestDTO = {
      name: formValue.name,
      abn: formValue.abn,
      acn: formValue.acn,
      billingEmail: formValue.billingEmail,
      accountsContactName: formValue.accountsContactName || undefined,
      accountsContactNumber: formValue.accountsContactNumber || undefined,
      industry: this.company?.industry,
      description: this.company?.description,
      website: this.company?.website,
      employeeCount: this.company?.employeeCount,
      isBillingPrimary: this.isSameAsCompanyDetails,
      primaryAddress: {
        addressLine1: formValue.addressLine1,
        addressLine2: formValue.addressLine2 || undefined,
        suburb: formValue.suburb || undefined,
        stateId: primaryStateId,
        zipCodeId: primaryZipCodeId,
      },
      billingAddress: this.isSameAsCompanyDetails
        ? {
            addressLine1: formValue.addressLine1,
            addressLine2: formValue.addressLine2 || undefined,
            suburb: formValue.suburb || undefined,
            stateId: primaryStateId,
            zipCodeId: primaryZipCodeId,
          }
        : {
            addressLine1: formValue.billingAddressLine1,
            addressLine2: formValue.billingAddressLine2 || undefined,
            suburb: formValue.billingSuburb || undefined,
            stateId: billingStateId,
            zipCodeId: billingZipCodeId,
          },
    };

    console.log('✅ Built company request:', request);
    return request;
  }

  private buildTempCompany(tempCompanyRequest: CompanyRequestDTO): ICompanyFields {
    return {
      ...this.companyStateService.getTempCompanyData(),
      id: this.company?.id,
      name: tempCompanyRequest.name,
      abn: tempCompanyRequest.abn,
      acn: tempCompanyRequest.acn,
      billingEmail: tempCompanyRequest.billingEmail,
      accountsContactName: tempCompanyRequest.accountsContactName,
      accountsContactNumber: tempCompanyRequest.accountsContactNumber,
      industry: tempCompanyRequest.industry,
      description: tempCompanyRequest.description,
      website: tempCompanyRequest.website,
      employeeCount: tempCompanyRequest.employeeCount,
      primaryAddress: {
        ...tempCompanyRequest.primaryAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Primary,
        stateName: this.states.find((s) => s.id === tempCompanyRequest.primaryAddress?.stateId)?.stateName,
        zipCode: this.postalCode.find((z) => z.id === tempCompanyRequest.primaryAddress?.zipCodeId)?.zipCode,
      },
      billingAddress: {
        ...tempCompanyRequest.billingAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Billing,
        stateName: this.states.find((s) => s.id === tempCompanyRequest.billingAddress?.stateId)?.stateName,
        zipCode: this.billingZipcode.find((z) => z.id === tempCompanyRequest.billingAddress?.zipCodeId)?.zipCode,
      },
    };
  }

  private updateCompanyDetails(
    tempCompanyRequest: CompanyRequestDTO,
    tempCompany: ICompanyFields,
  ): void {
    console.log('🚀 Updating company with ID:', this.company?.id);
    console.log('🚀 Request payload:', tempCompanyRequest);

    this.isLoading = true;
    this.subscriptions.add(
      this.registerService
        .updateCompany(this.company?.id!, tempCompanyRequest)
        .subscribe({
          next: (response: ApiResponseCompanyDTO) => {
            console.log('✅ Update response:', response);
            this.handleUpdateSuccess(response, tempCompany);
          },
          error: (error) => {
            console.error('❌ Update error:', error);
            this.handleUpdateError(error);
          },
        }),
    );
  }

  private handleUpdateSuccess(
    response: ApiResponseCompanyDTO,
    tempCompany: ICompanyFields,
  ): void {
    this.isLoading = false;
    if (response.success && response.data) {
      this.company = { ...tempCompany, ...response.data };
      this.companyStateService.setCompanyData(this.company);
      this.companyStateService.clearTempCompanyData();
      this.isBillingDetailsAccordionOpen = false;
      this.isSameAsCompanyDetails = false;
      this.notification.success(
        COMMON_STRINGS.successMessages.companyUpdateSuccess,
      );
      this.companySaved.emit(response.data);
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
          '${errorMessage}',
          response.message || '',
        ),
      );
    }
  }

  private handleUpdateError(error: HttpErrorResponse): void {
    this.isLoading = false;
    this.notification.error(
      error.error?.message ||
        COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
          '${errorMessage}',
          error.message,
        ),
    );
  }

  onSameAsCompanyDetailsChange(checked: boolean): void {
    console.log('🔄 Same as company details changed:', checked);
    this.isSameAsCompanyDetails = checked;

    if (checked) {
      const primaryValues = {
        billingAddressLine1: this.companyForm.get('addressLine1')?.value || '',
        billingAddressLine2: this.companyForm.get('addressLine2')?.value || '',
        billingSuburb: this.companyForm.get('suburb')?.value || '',
        billingState: this.companyForm.get('state')?.value || '',
        billingPostalCode: this.companyForm.get('postalCode')?.value || '',
      };
      console.log('🔄 Copying primary values to billing:', primaryValues);
      this.companyForm.patchValue(primaryValues);
      this.billingZipcode = this.postalCode;
      this.disableBillingFields();
    } else {
      this.enableBillingFields();
    }
    this.cdr.detectChanges();
  }

  private disableBillingFields(): void {
    const billingFields = [
      'billingAddressLine1',
      'billingAddressLine2',
      'billingSuburb',
      'billingState',
      'billingPostalCode',
    ];

    billingFields.forEach((field) => {
      this.companyForm.get(field)?.disable();
    });
  }

  private enableBillingFields(): void {
    const billingFields = [
      'billingAddressLine1',
      'billingAddressLine2',
      'billingSuburb',
      'billingState',
      'billingPostalCode',
    ];

    billingFields.forEach((field) => {
      this.companyForm.get(field)?.enable();
    });
  }

  getCompanyErrorTip(controlName: string): string | undefined {
    const control = this.companyForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${
        controlName.charAt(0).toUpperCase() + controlName.slice(1)
      } is required`;
    }
    if (
      controlName === 'billingEmail' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'accountsContactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    const fieldsToCompare: (keyof ICompanyFields)[] = [
      'name',
      'abn',
      'acn',
      'billingEmail',
      'accountsContactName',
      'accountsContactNumber',
    ];

    for (const field of fieldsToCompare) {
      if (original[field] !== updated[field]) {
        return false;
      }
    }

    const addressFields: (keyof AddressResponseDTO)[] = [
      'addressLine1',
      'addressLine2',
      'suburb',
      'stateId',
      'zipCodeId',
    ];

    for (const field of addressFields) {
      if (
        (original.primaryAddress?.[field] ?? '') !==
        (updated.primaryAddress?.[field] ?? '')
      ) {
        return false;
      }
      if (
        (original.billingAddress?.[field] ?? '') !==
        (updated.billingAddress?.[field] ?? '')
      ) {
        return false;
      }
    }

    return true;
  }

  toggleBillingDetailsAccordion(): void {
    this.isBillingDetailsAccordionOpen = !this.isBillingDetailsAccordionOpen;
  }
}