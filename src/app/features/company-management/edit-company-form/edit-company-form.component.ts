import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ChangeDetectorRef,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of, Subscription } from 'rxjs';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
  ApiResponseListStateDTO,
  ApiResponseListZipCodeDTO,
  ApiResponseCompanyDTO,
  CompanyRequestDTO,
  AddressResponseDTO,
} from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { CompanyLookupService } from '../../../core/services/company-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';

@Component({
  selector: 'app-edit-company-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company-form.component.html',
  styleUrls: [
    './edit-company-form.component.scss',
    '../view-company/view-company.component.scss',
  ],
})
export class EditCompanyFormComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() company: ICompanyFields | null = null;
  @Output() companySaved = new EventEmitter<CompanyDTO>();
  @Output() formCancelled = new EventEmitter<void>();

  companyForm: FormGroup;
  isLoading = false;
  isBillingDetailsAccordionOpen = false;
  isSameAsCompanyDetails = false;

  states$: Observable<StateDTO[]> = of([]);
  postalCode$: Observable<ZipCodeDTO[]> = of([]);
  billingZipcode$: Observable<ZipCodeDTO[]> = of([]);

  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyStateService: CompanyStateService,
    private companyLookupService: CompanyLookupService,
    private cdr: ChangeDetectorRef,
  ) {
    this.companyForm = this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      billingAddressLine1: ['', Validators.required],
      billingAddressLine2: [''],
      billingSuburb: [''],
      billingState: ['', Validators.required],
      billingPostalCode: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    console.log('in edit company');
    if (this.company) {
      this.initializeForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isSaveButtonEnabled(): boolean {
    return this.companyForm.valid && this.companyForm.dirty && !this.isLoading;
  }

  initializeForm(): void {
    if (!this.company) return;

    this.fetchStates();
    this.subscriptions.add(
      this.states$.subscribe((states: StateDTO[]) => {
        this.populateFormWithCompanyData(states);
        this.setupFormSubscriptions();
      }),
    );
  }

  private populateFormWithCompanyData(states: StateDTO[]): void {
    if (!this.company) return;

    console.log('🔍 Company data:', this.company);
    console.log('🔍 Available states:', states);

    // Find state IDs by matching state names
    const primaryState = states.find(
      (s) => s.stateName === this.company?.primaryAddress?.stateName
    );
    const billingState = states.find(
      (s) => s.stateName === this.company?.billingAddress?.stateName
    );

    const primaryStateId = primaryState?.id || null;
    const billingStateId = billingState?.id || null;

    console.log('🔍 Primary state found:', primaryState, 'ID:', primaryStateId);
    console.log('🔍 Billing state found:', billingState, 'ID:', billingStateId);

    // Populate form with company data
    this.companyForm.patchValue({
      name: this.company.name || '',
      abn: this.company.abn || '',
      acn: this.company.acn || '',
      billingEmail: this.company.billingEmail || '',
      accountsContactName: this.company.accountsContactName || '',
      accountsContactNumber: this.company.accountsContactNumber || '',
      addressLine1: this.company.primaryAddress?.addressLine1 || '',
      addressLine2: this.company.primaryAddress?.addressLine2 || '',
      suburb: this.company.primaryAddress?.suburb || '',
      state: primaryStateId,
      billingAddressLine1: this.company.billingAddress?.addressLine1 || '',
      billingAddressLine2: this.company.billingAddress?.addressLine2 || '',
      billingSuburb: this.company.billingAddress?.suburb || '',
      billingState: billingStateId,
    });

    console.log('✅ Form populated with values:', this.companyForm.value);

    // Check if billing address is same as primary address
    this.checkIfBillingIsSameAsPrimary();

    // Fetch zipcodes for both addresses
    this.fetchZipcodesForAddresses(primaryStateId, billingStateId);

    // Mark form as pristine after initial population
    setTimeout(() => {
      this.companyForm.markAsPristine();
      console.log('✅ Form marked as pristine');
    }, 1000); // Wait longer for postal codes to be populated
  }

  private setupFormSubscriptions(): void {
    // Primary address state change
    this.subscriptions.add(
      this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        if (stateId) {
          this.fetchZipcodes(stateId, 'primary');
        }
      }),
    );

    // Billing address state change
    this.subscriptions.add(
      this.companyForm
        .get('billingState')
        ?.valueChanges.subscribe((stateId) => {
          if (stateId) {
            this.fetchZipcodes(stateId, 'billing');
          }
        }),
    );

    // Track form changes for save button state
    this.subscriptions.add(
      this.companyForm.valueChanges.subscribe(() => {
        // Force change detection when form changes
        this.cdr.detectChanges();
      })
    );
  }

  private checkIfBillingIsSameAsPrimary(): void {
    // Always start with checkbox unchecked - user should manually choose
    this.isSameAsCompanyDetails = false;
    // Ensure billing fields are enabled by default
    this.enableBillingFields();
  }

  private fetchZipcodesForAddresses(
    primaryStateId: number | null,
    billingStateId: number | null,
  ): void {
    console.log('🔍 Fetching zipcodes for states:', { primaryStateId, billingStateId });

    if (primaryStateId) {
      this.fetchZipcodes(primaryStateId, 'primary');
    }
    if (billingStateId) {
      this.fetchZipcodes(billingStateId, 'billing');
    }
  }

  fetchStates(): void {
    this.isLoading = true;
    this.subscriptions.add(
      this.companyLookupService.fetchStates().subscribe({
        next: (response: ApiResponseListStateDTO) => {
          this.isLoading = false;
          const states = response.data || [];
          this.states$ = of(states);
          if (states.length === 0) {
            this.notification.warning(
              COMMON_STRINGS.errorMessages.failedToFetchStates,
            );
          }
        },
        error: () => {
          this.isLoading = false;
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToFetchStates,
          );
        },
      }),
    );
  }

  fetchZipcodes(stateId: number, addressType: 'primary' | 'billing'): void {
    if (!stateId) return;

    this.isLoading = true;
    this.subscriptions.add(
      this.companyLookupService.fetchZipcodes(stateId).subscribe({
        next: (response: ApiResponseListZipCodeDTO) => {
          this.isLoading = false;
          const zipcodes = response.data || [];

          if (addressType === 'primary') {
            this.postalCode$ = of(zipcodes);
            // Set postal code if company data exists
            if (this.company?.primaryAddress?.zipCode) {
              const matchingZipcode = zipcodes.find(
                (z) => z.zipCode === this.company?.primaryAddress?.zipCode,
              );
              if (matchingZipcode) {
                this.companyForm
                  .get('postalCode')
                  ?.setValue(matchingZipcode.id);
                console.log('✅ Set primary postal code:', matchingZipcode.id, 'for zipCode:', this.company?.primaryAddress?.zipCode);
              } else {
                console.log('❌ No matching postal code found for:', this.company?.primaryAddress?.zipCode);
              }
            }
          } else {
            this.billingZipcode$ = of(zipcodes);
            // Set billing postal code if company data exists
            if (this.company?.billingAddress?.zipCode) {
              const matchingZipcode = zipcodes.find(
                (z) => z.zipCode === this.company?.billingAddress?.zipCode,
              );
              if (matchingZipcode) {
                this.companyForm
                  .get('billingPostalCode')
                  ?.setValue(matchingZipcode.id);
                console.log('✅ Set billing postal code:', matchingZipcode.id, 'for zipCode:', this.company?.billingAddress?.zipCode);
              } else {
                console.log('❌ No matching billing postal code found for:', this.company?.billingAddress?.zipCode);
              }
            }
          }

          if (zipcodes.length === 0) {
            this.notification.warning(
              COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
            );
          }
        },
        error: () => {
          this.isLoading = false;
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
        },
      }),
    );
  }

  saveCompanyDetails(): void {
    if (!this.companyForm.valid) {
      this.handleInvalidForm();
      return;
    }

    const tempCompanyRequest = this.buildCompanyRequest();
    const tempCompany = this.buildTempCompany(tempCompanyRequest);
    if (this.company?.id) {
      this.updateCompanyDetails(tempCompanyRequest, tempCompany);
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
    }
  }

  cancelEdit(): void {
    this.companyStateService.clearTempCompanyData();
    this.companyForm.reset();
    this.isSameAsCompanyDetails = false;
    this.isBillingDetailsAccordionOpen = false;
    this.formCancelled.emit();
  }

  private handleInvalidForm(): void {
    this.companyForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private buildCompanyRequest(): CompanyRequestDTO {
    const formValue = this.companyForm.getRawValue();

    console.log('🔍 Building company request with form values:', formValue);

    const request = {
      name: formValue.name,
      abn: formValue.abn,
      acn: formValue.acn,
      billingEmail: formValue.billingEmail,
      accountsContactName: formValue.accountsContactName || undefined,
      accountsContactNumber: formValue.accountsContactNumber || undefined,
      industry: this.company?.industry,
      description: this.company?.description,
      website: this.company?.website,
      employeeCount: this.company?.employeeCount,
      isBillingPrimary: this.isSameAsCompanyDetails,
      primaryAddress: {
        addressLine1: formValue.addressLine1,
        addressLine2: formValue.addressLine2 || undefined,
        suburb: formValue.suburb || undefined,
        stateId: Number(formValue.state),
        zipCodeId: Number(formValue.postalCode),
      },
      billingAddress: this.isSameAsCompanyDetails ? {
        addressLine1: formValue.addressLine1,
        addressLine2: formValue.addressLine2 || undefined,
        suburb: formValue.suburb || undefined,
        stateId: Number(formValue.state),
        zipCodeId: Number(formValue.postalCode),
      } : {
        addressLine1: formValue.billingAddressLine1,
        addressLine2: formValue.billingAddressLine2 || undefined,
        suburb: formValue.billingSuburb || undefined,
        stateId: Number(formValue.billingState),
        zipCodeId: Number(formValue.billingPostalCode),
      },
    };

    console.log('✅ Built company request:', request);
    return request;
  }

  private buildTempCompany(
    tempCompanyRequest: CompanyRequestDTO,
  ): ICompanyFields {
    return {
      ...this.companyStateService.getTempCompanyData(),
      name: tempCompanyRequest.name,
      abn: tempCompanyRequest.abn,
      acn: tempCompanyRequest.acn,
      billingEmail: tempCompanyRequest.billingEmail,
      accountsContactName: tempCompanyRequest.accountsContactName,
      accountsContactNumber: tempCompanyRequest.accountsContactNumber,
      primaryAddress: {
        ...tempCompanyRequest.primaryAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Primary,
      },
      billingAddress: {
        ...tempCompanyRequest.billingAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Billing,
      },
    };
  }

  private updateCompanyDetails(
    tempCompanyRequest: CompanyRequestDTO,
    tempCompany: ICompanyFields,
  ): void {
    console.log('🚀 Updating company with ID:', this.company?.id);
    console.log('🚀 Request payload:', tempCompanyRequest);

    this.isLoading = true;
    this.registerService
      .updateCompany(this.company?.id!, tempCompanyRequest)
      .subscribe({
        next: (response: ApiResponseCompanyDTO) => {
          console.log('✅ Update response:', response);
          this.handleUpdateSuccess(response, tempCompany);
        },
        error: (error) => {
          console.error('❌ Update error:', error);
          this.handleUpdateError(error);
        },
      });
  }

  private handleUpdateSuccess(
    response: ApiResponseCompanyDTO,
    tempCompany: ICompanyFields,
  ): void {
    if (response.success && response.data) {
      this.company = { ...tempCompany, ...response.data };
      this.companyStateService.setCompanyData(this.company);
      this.companyStateService.clearTempCompanyData();
      this.isBillingDetailsAccordionOpen = false;
      this.isSameAsCompanyDetails = false;
      this.notification.success(
        COMMON_STRINGS.successMessages.companyUpdateSuccess,
      );
      this.isLoading = false;
      // Navigate back to company list or emit to parent
      this.companySaved.emit(response.data);
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
          '${errorMessage}',
          response.message || '',
        ),
      );
      this.isLoading = false;
    }
  }

  private handleUpdateError(error: HttpErrorResponse): void {
    this.isLoading = false;
    this.notification.error(
      error.error?.message ||
        COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
          '${errorMessage}',
          error.message,
        ),
    );
  }

  onSameAsCompanyDetailsChange(checked: boolean): void {
    this.isSameAsCompanyDetails = checked;

    if (checked) {
      // Copy primary address to billing address
      const primaryValues = {
        billingAddressLine1: this.companyForm.get('addressLine1')?.value || '',
        billingAddressLine2: this.companyForm.get('addressLine2')?.value || '',
        billingSuburb: this.companyForm.get('suburb')?.value || '',
        billingState: this.companyForm.get('state')?.value || '',
        billingPostalCode: this.companyForm.get('postalCode')?.value || '',
      };
      this.companyForm.patchValue(primaryValues);

      // Update billing zipcode observable to match primary
      this.billingZipcode$ = this.postalCode$;

      // Disable billing fields
      this.disableBillingFields();
    } else {
      // Enable billing fields
      this.enableBillingFields();
    }
  }

  private disableBillingFields(): void {
    const billingFields = [
      'billingAddressLine1',
      'billingAddressLine2',
      'billingSuburb',
      'billingState',
      'billingPostalCode'
    ];

    billingFields.forEach(field => {
      this.companyForm.get(field)?.disable();
    });
  }

  private enableBillingFields(): void {
    const billingFields = [
      'billingAddressLine1',
      'billingAddressLine2',
      'billingSuburb',
      'billingState',
      'billingPostalCode'
    ];

    billingFields.forEach(field => {
      this.companyForm.get(field)?.enable();
    });
  }

  getCompanyErrorTip(controlName: string): string | undefined {
    const control = this.companyForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'billingEmail' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'accountsContactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    const fieldsToCompare: (keyof ICompanyFields)[] = [
      'name',
      'abn',
      'acn',
      'billingEmail',
      'accountsContactName',
      'accountsContactNumber',
    ];

    for (const field of fieldsToCompare) {
      if (original[field] !== updated[field]) {
        return false;
      }
    }

    const addressFields: (keyof AddressResponseDTO)[] = [
      'addressLine1',
      'addressLine2',
      'suburb',
      'stateId',
      'zipCodeId',
    ];

    for (const field of addressFields) {
      if (
        (original.primaryAddress?.[field] ?? '') !==
        (updated.primaryAddress?.[field] ?? '')
      ) {
        return false;
      }
      if (
        (original.billingAddress?.[field] ?? '') !==
        (updated.billingAddress?.[field] ?? '')
      ) {
        return false;
      }
    }

    return true;
  }

  toggleBillingDetailsAccordion(): void {
    this.isBillingDetailsAccordionOpen = !this.isBillingDetailsAccordionOpen;
  }
}
